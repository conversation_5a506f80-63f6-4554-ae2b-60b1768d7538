# EWH Landing Zone - Cấu trúc Folder

## C<PERSON>u trúc hiện tại (đã đượ<PERSON> sử<PERSON>)

```
ewh-landingzone/
├── main.tf                    # Root module - gọi core module
├── variables.tf               # Root variables
├── terraform.tfvars          # C<PERSON>u hình thực tế (đã chuyển từ core/)
├── terraform.tfvars.example  # Cấu hình mẫu
├── README.md                  # Tài liệu chính
├── FIXES_APPLIED.md          # Danh sách các lỗi đã sửa
├── FOLDER_STRUCTURE.md       # File này
├── core/                     # Core module
│   ├── main.tf              # Enterprise Scale + Azure AD groups
│   ├── variables.tf         # Core module variables
│   ├── outputs.tf           # Core module outputs
│   ├── terraform.tf         # Provider requirements
│   ├── lib/                 # Custom policies và archetypes
│   │   ├── archetype_definition_productions.json
│   │   ├── archetype_definition_non_productions.json
│   │   ├── policy_assignments/
│   │   └── policy_definitions/
│   └── modules/
│       └── azure_ad_groups/ # Azure AD groups module
│           ├── main.tf
│           ├── variables.tf
│           └── outputs.tf
├── connectivity/            # Connectivity module (commented out)
└── management/             # Management module (commented out)
```

## Thay đổi chính đã thực hiện

### 1. ✅ File terraform.tfvars
- **Trước**: Nằm trong `core/terraform.tfvars`
- **Sau**: Chuyển ra `terraform.tfvars` ở root level
- **Lý do**: Root module cần đọc variables từ root level

### 2. ✅ Cấu trúc Core Module
- **Đúng**: Core module chỉ chứa logic, không chứa terraform.tfvars
- **Đúng**: Library path trỏ đến `${path.module}/lib`
- **Đúng**: Sử dụng archetype definitions từ lib folder

### 3. ✅ Provider Configuration
- **Root level**: Cấu hình azurerm và azuread providers
- **Core level**: Chỉ khai báo required_providers

## Cách hoạt động

1. **Root module** (`main.tf`) đọc variables từ `terraform.tfvars`
2. **Root module** truyền variables xuống **core module**
3. **Core module** sử dụng variables để:
   - Tạo Enterprise Scale management groups
   - Tạo Azure AD groups (nếu được cấu hình)
   - Áp dụng custom policies từ lib folder

## Kiểm tra cấu hình

```bash
# Kiểm tra syntax
terraform validate

# Xem plan (không deploy)
terraform plan

# Deploy (khi sẵn sàng)
terraform apply
```

## Lưu ý quan trọng

1. **terraform.tfvars**: Chứa cấu hình thực tế, cần cập nhật subscription IDs
2. **Azure AD Groups**: Hiện tại được cấu hình đầy đủ, có thể set `azure_ad_groups = {}` để disable
3. **Connectivity/Management**: Modules này đang được comment out, uncomment khi cần
4. **Library Path**: Đã được sửa để trỏ đúng đến core/lib folder

## Trạng thái hiện tại

- ✅ Terraform validate: PASS
- ✅ Cấu trúc folder: ĐÚNG
- ✅ Variables: ĐẦY ĐỦ
- ✅ Providers: CẤU HÌNH ĐÚNG
- ✅ Custom archetypes: HOẠT ĐỘNG

Cấu hình đã sẵn sàng để deploy!
