# Fixes Applied to EWH Landing Zone

## Issues Found and Fixed

### 1. Missing Required Variables
**Problem**: The core module required `azure_ad_groups` variable but it wasn't provided in main.tf
**Fix**: Added the missing variable to the module call in main.tf

### 2. Unsupported Arguments
**Problem**: Several arguments were being passed to the core module that it didn't expect:
- `configure_connectivity_resources`
- `configure_management_resources` 
- `primary_location`
- `secondary_location`
- `subscription_id_connectivity`
- `subscription_id_identity`
- `subscription_id_management`

**Fix**: Removed these unsupported arguments from the core module call

### 3. Missing Provider Configuration
**Problem**: The core module uses Azure AD provider but it wasn't configured in main.tf
**Fix**: Added azuread provider configuration to main.tf

### 4. Missing Variables in Root Module
**Problem**: Root variables.tf was missing Azure AD groups related variables
**Fix**: Added the following variables:
- `azure_ad_groups`
- `assign_roles_to_all_subscriptions`
- `enable_directory_role_assignments`

### 5. Updated terraform.tfvars.example
**Problem**: Example configuration file didn't include Azure AD groups settings
**Fix**: Added Azure AD groups configuration examples with default empty values

## Current Configuration

The configuration now:
- ✅ Passes `terraform validate`
- ✅ Passes `terraform plan` 
- ✅ Creates proper Enterprise Scale management group hierarchy
- ✅ Supports Azure AD groups creation (currently disabled with empty config)
- ✅ Uses correct archetype definitions for custom landing zones

## Next Steps

1. **Configure Azure AD Groups**: Update the `azure_ad_groups` variable in terraform.tfvars with actual group configurations if needed
2. **Test Deployment**: Run `terraform apply` to deploy the infrastructure
3. **Enable Additional Modules**: Uncomment connectivity and management modules in main.tf when ready
4. **Update Subscription IDs**: Replace placeholder subscription IDs in terraform.tfvars.example with actual values

### 6. Fixed File Structure
**Problem**: terraform.tfvars file was in core folder instead of root level
**Fix**: Moved terraform.tfvars from core/ to root level and merged with terraform.tfvars.example

### 7. Fixed Library Path
**Problem**: library_path was pointing to wrong directory (${path.root}/lib instead of ${path.module}/lib)
**Fix**: Updated library_path in core/main.tf to point to correct location

## Files Modified

- `main.tf` - Fixed module configuration and added azuread provider
- `variables.tf` - Added missing Azure AD groups variables
- `terraform.tfvars.example` - Added Azure AD groups configuration examples
- `terraform.tfvars` - Created complete configuration file (moved from core/)
- `core/main.tf` - Fixed archetype IDs and library_path for custom landing zones
- `core/terraform.tfvars` - Removed (moved to root level)
