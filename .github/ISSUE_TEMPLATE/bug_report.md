---
name: "Bug report \U0001F41B"
about: Report errors or unexpected behaviour
title: 'Bug Report'
labels: 'needs triage :warning:'
assignees: ''
---

### Community Note

<!--- Please keep this note for the community --->

- Please vote on this issue by adding a 👍 [reaction](https://blog.github.com/2016-03-10-add-reactions-to-pull-requests-issues-and-comments/) to the original issue to help the community and maintainers prioritize this request
- Please do not leave "+1" or "me too" comments, they generate extra noise for issue followers and do not help prioritize the request
- If you are interested in working on this issue or have submitted a pull request, please leave a comment

<!--- Thank you for keeping this note for the community --->

### Versions

<!-- Please tell us the versions of terraform, azure provider and this module you are using, to help us replicate the issue. -->

**terraform**:

**azure provider**:

**module**:

### Description

#### Describe the bug

<!-- A clear and concise description of what the bug is. -->

#### Steps to Reproduce

<!-- Please provide detailed steps for reproducing the issue. -->

1. step 1
2. step 2
3. you get it...

#### Screenshots

<!-- If applicable, add screenshots to help explain your problem. -->

#### Additional context

<!-- Add any other context about the problem here. -->
