---
name: "Feature request \U0001F680"
about: Suggest an idea for this project
title: 'Feature Request'
labels: 'needs triage :warning:'
assignees: ''
---

### Community Note

<!--- Please keep this note for the community --->

- Please vote on this issue by adding a 👍 [reaction](https://blog.github.com/2016-03-10-add-reactions-to-pull-requests-issues-and-comments/) to the original issue to help the community and maintainers prioritize this request
- Please do not leave "+1" or "me too" comments, they generate extra noise for issue followers and do not help prioritize the request
- If you are interested in working on this issue or have submitted a pull request, please leave a comment

<!--- Thank you for keeping this note for the community --->

### Description

#### Is your feature request related to a problem?

<!-- A clear and concise description of what the problem is. Ex. I'm always frustrated when [...] -->

#### Describe the solution you'd like

<!-- A clear and concise description of what you want to happen. -->

<!-- A clear and concise description of any alternative solutions or features you've considered. -->

#### Additional context
