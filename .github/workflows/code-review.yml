---
name: Code Review

##########################################
# Start the job on push for all branches #
##########################################

# yamllint disable-line rule:truthy
on:
  pull_request:
    branches:
      - main
  workflow_dispatch: {}

permissions:
  contents: read
  packages: read
  statuses: write

###############
# Set the Job #
###############
jobs:
  build:
    name: Lint code base
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # - name: Run tflint
      #   run: |
      #     CONFIG_FILE=$(realpath ./.github/linters/.tflint.hcl)
      #     tflint --init --config=$CONFIG_FILE
      #     tflint --config=$CONFIG_FILE
      #     tflint --chdir=./examples/400-multi-with-orchestration --config=$CONFIG_FILE
      #     tflint --chdir=./examples/400-multi-with-remote-state/connectivity --config=$CONFIG_FILE
      #     tflint --chdir=./examples/400-multi-with-remote-state/core --config=$CONFIG_FILE
      #     tflint --chdir=./examples/400-multi-with-remote-state/management --config=$CONFIG_FILE

      - name: Lint Code Base
        uses: github/super-linter@v6
        env:
          VALIDATE_ALL_CODEBASE: false
          # Need to define main branch as default
          # is set to master in super-linter
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # The following linter types will be enabled:
          VALIDATE_BASH: true
          VALIDATE_BASH_EXEC: true
          VALIDATE_GITHUB_ACTIONS: true
          VALIDATE_JSON: true
          VALIDATE_MARKDOWN: true
          VALIDATE_POWERSHELL: true
          VALIDATE_TERRAFORM_TFLINT: true
          VALIDATE_YAML: true
          # Additional settings:
          # If a shell script is not executable, the bash-exec
          # linter will report an error when set to true
          ERROR_ON_MISSING_EXEC_BIT: true
