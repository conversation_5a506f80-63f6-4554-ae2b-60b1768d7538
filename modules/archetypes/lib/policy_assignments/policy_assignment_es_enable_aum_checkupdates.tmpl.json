{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enable-AUM-CheckUpdates", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Configure auto-assessment (every 24 hours) for OS updates. You can control the scope of assignment according to machine subscription, resource group, location or tag. Learn more about this for Windows: https://aka.ms/computevm-windowspatchassessmentmode, for Linux: https://aka.ms/computevm-linuxpatchassessmentmode.", "displayName": "Configure periodic checking for missing system updates on azure virtual machines and Arc-enabled virtual machines.", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Deploy-AUM-CheckUpdates", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Periodic checking of missing updates {enforcementMode} be enabled."}], "parameters": {"assessmentMode": {"value": "AutomaticByPlatform"}, "locations": {"value": []}, "tagValues": {"value": {}}, "tagOperator": {"value": "Any"}}, "scope": "${current_scope_resource_id}", "notScopes": []}}