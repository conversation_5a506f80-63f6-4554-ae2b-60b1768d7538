{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "Audit-ResourceRGLocation", "dependsOn": [], "properties": {"description": "Resource Group and Resource locations should match.", "displayName": "Resource Group and Resource locations should match", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "definitionVersion": "2.*.*", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Resources {enforcementMode} be deployed in the same region as the Resource Group."}], "parameters": {}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}