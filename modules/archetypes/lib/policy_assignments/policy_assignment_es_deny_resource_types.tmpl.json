{"name": "Deny-Resource-Types", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Specifies the Resource Types to deny deployment by policy.", "displayName": "Deny-Resource-Types", "notScopes": [], "parameters": {"listOfResourceTypesNotAllowed": {"value": ["conexlink.mycloudit/accounts"]}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749", "nonComplianceMessages": [{"message": "Resources {enforcementMode} be in the allowed types."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "None"}}