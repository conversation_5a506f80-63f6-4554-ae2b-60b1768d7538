{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deploy-Log-Analytics", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Deploy resource group containing Log Analytics workspace and linked automation account to centralize logs and monitoring. The automation account is aprerequisite for solutions like Updates and Change Tracking.", "displayName": "Configure Log Analytics workspace and automation account to centralize logs and monitoring", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8e3e61b3-0b32-22d5-4edf-55f87fdb5955", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Log Analytics workspace and automation account {enforcementMode} be configured to centralize logs and monitoring."}], "parameters": {"workspaceName": {"value": "${root_scope_id}-la"}, "automationAccountName": {"value": "${root_scope_id}-automation"}, "workspaceRegion": {"value": "${default_location}"}, "automationRegion": {"value": "${default_location}"}, "rgName": {"value": "${root_scope_id}-mgmt"}, "dataRetention": {"value": "30"}}, "scope": "${current_scope_resource_id}", "notScopes": []}}