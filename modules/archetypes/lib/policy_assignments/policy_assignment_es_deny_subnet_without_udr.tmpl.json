{"name": "Deny-Subnet-Without-Udr", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy denies the creation of a subnet without a User-Defined Route to control traffic flow.", "displayName": "Subnets should have a User-Defined Route", "notScopes": [], "parameters": {}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Udr", "nonComplianceMessages": [{"message": "Subnets {enforcementMode} have a User-Defined Route."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "None"}}