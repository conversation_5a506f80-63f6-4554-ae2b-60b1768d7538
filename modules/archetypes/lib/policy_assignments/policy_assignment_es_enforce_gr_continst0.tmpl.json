{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-GR-ContInst0", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables additional ALZ guardrails for Container Instance.", "displayName": "Enforce recommended guardrails for Container Instance", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-ContainerInstance", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Recommended guardrails {enforcementMode} be enforced for Container Instance."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}