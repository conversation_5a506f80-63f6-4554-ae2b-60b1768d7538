{"name": "Deny-RDP-From-Internet", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy denies any network security rule that allows RDP access from Internet.", "displayName": "RDP access from the Internet should be blocked", "notScopes": [], "parameters": {}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-RDP-From-Internet", "nonComplianceMessages": [{"message": "RDP access from the internet {enforcementMode} be blocked."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "None"}}