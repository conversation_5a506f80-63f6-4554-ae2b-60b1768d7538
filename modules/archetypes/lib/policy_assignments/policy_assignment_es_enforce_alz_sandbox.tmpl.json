{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-ALZ-Sandbox", "location": "${default_location}", "dependsOn": [], "properties": {"description": "This initiative will help enforce and govern subscriptions that are placed within the Sandbox Management Group. See https://aka.ms/alz/policies for more information.", "displayName": "Enforce ALZ Sandbox Guardrails", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-ALZ-Sandbox", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "ALZ Sandbox Guardrails {enforcementMode} be enforced."}], "parameters": {"listOfResourceTypesNotAllowed": {"value": ["microsoft.network/expressroutecircuits", "microsoft.network/expressroutegateways", "microsoft.network/expressrouteports", "microsoft.network/virtualwans", "microsoft.network/virtualhubs", "microsoft.network/vpngateways", "microsoft.network/p2svpngateways", "microsoft.network/vpnsites", "microsoft.network/virtualnetworkgateways"]}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "identity": {"type": "None"}}