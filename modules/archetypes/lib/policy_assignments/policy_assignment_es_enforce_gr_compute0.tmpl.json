{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-GR-Compute0", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables additional ALZ guardrails for Compute.", "displayName": "Enforce recommended guardrails for Compute", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-Compute", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Recommended guardrails {enforcementMode} be enforced for Compute."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}