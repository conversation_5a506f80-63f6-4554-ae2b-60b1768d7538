{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-GR-KeyVaultSup0", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables additional ALZ guardrails for Key Vault Supplementary.", "displayName": "Enforce recommended guardrails for Key Vault Supplementary", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-KeyVault-Sup", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Recommended guardrails {enforcementMode} be enforced for Key Vault Supplementary."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}