{"name": "Deny-Private-DNS-Zones", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy denies the creation of a private DNS in the current scope, used in combination with policies that create centralized private DNS in connectivity subscription.", "displayName": "Deny the creation of private DNS", "notScopes": [], "parameters": {}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Private-DNS-Zones", "nonComplianceMessages": [{"message": "Private DNS {enforcementMode} be created centrally in the connectivity subscription."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "None"}}