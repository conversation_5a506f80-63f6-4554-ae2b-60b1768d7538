{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "Deny-Priv-Esc-AKS", "dependsOn": [], "properties": {"description": "Do not allow containers to run with privilege escalation to root in a Kubernetes cluster. This recommendation is part of CIS 5.2.5 which is intended to improve the security of your Kubernetes environments. This policy is generally available for Kubernetes Service (AKS), and preview for AKS Engine and Azure Arc enabled Kubernetes. For more information, see https://aka.ms/kubepolicydoc.", "displayName": "Kubernetes clusters should not allow container privilege escalation", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1c6e92c9-99f0-4e55-9cf2-0c234dc48f99", "definitionVersion": "7.*.*", "enforcementMode": "<PERSON><PERSON><PERSON>", "parameters": {"effect": {"value": "deny"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}