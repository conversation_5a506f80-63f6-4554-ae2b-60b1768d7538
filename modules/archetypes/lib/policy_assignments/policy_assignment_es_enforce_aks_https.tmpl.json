{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "Enforce-AKS-HTTPS", "dependsOn": [], "properties": {"description": "Use of HTTPS ensures authentication and protects data in transit from network layer eavesdropping attacks. This capability is currently generally available for Kubernetes Service (AKS), and in preview for AKS Engine and Azure Arc enabled Kubernetes. For more info, visit https://aka.ms/kubepolicydoc", "displayName": "Kubernetes clusters should be accessible only over HTTPS", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1a5b4dca-0b6f-4cf5-907c-56316bc1bf3d", "definitionVersion": "8.*.*", "enforcementMode": "<PERSON><PERSON><PERSON>", "parameters": {"effect": {"value": "deny"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}