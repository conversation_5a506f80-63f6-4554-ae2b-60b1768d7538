{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-GR-APIM0", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables additional ALZ guardrails for API Management.", "displayName": "Enforce recommended guardrails for API Management", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-APIM", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Recommended guardrails {enforcementMode} be enforced for API Management."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}