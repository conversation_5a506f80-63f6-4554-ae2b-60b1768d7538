{"name": "Deny-DataB-Vnet", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Enforces the use of vnet injection for Databricks workspaces.", "displayName": "Enforces the use of vnet injection for Databricks", "notScopes": [], "parameters": {"effect": {"value": "<PERSON><PERSON>"}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Databricks-VirtualNetwork", "nonComplianceMessages": [{"message": "Databricks workspaces {enforcementMode} use vnet injection."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}