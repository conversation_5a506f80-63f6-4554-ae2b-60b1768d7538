{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "Deny-IP-forwarding", "dependsOn": [], "properties": {"description": "This policy denies the network interfaces which enabled IP forwarding. The setting of IP forwarding disables Azure's check of the source and destination for a network interface. This should be reviewed by the network security team.", "displayName": "Network interfaces should disable IP forwarding", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Network interfaces {enforcementMode} disable IP forwarding."}], "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900", "definitionVersion": "1.*.*", "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}, "location": "${default_location}", "identity": {"type": "None"}}