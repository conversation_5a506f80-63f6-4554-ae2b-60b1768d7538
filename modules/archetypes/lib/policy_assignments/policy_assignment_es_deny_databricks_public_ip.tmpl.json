{"name": "Deny-DataB-<PERSON>p", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Prevent the deployment of Databricks workspaces that do not use the noPublicIp feature to host Databricks clusters without public IPs.", "displayName": "Prevent usage of Databricks with public IP", "notScopes": [], "parameters": {"effect": {"value": "<PERSON><PERSON>"}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Databricks-NoPublicIp", "nonComplianceMessages": [{"message": "Databricks workspace {enforcementMode} use the noPublicIp feature to host Databricks clusters."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}