{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-GR-ContApps0", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables additional ALZ guardrails for Container Apps.", "displayName": "Enforce recommended guardrails for Container Apps", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-ContainerApps", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Recommended guardrails {enforcementMode} be enforced for Container Apps."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}