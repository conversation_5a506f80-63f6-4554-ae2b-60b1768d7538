{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "Deploy-MDFC-SqlAtp", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Enable Azure Defender on your SQL Servers and SQL Managed Instances to detect anomalous activities indicating unusual and potentially harmful attempts to access or exploit databases.", "displayName": "Configure Azure Defender to be enabled on SQL Servers and SQL Managed Instances", "policyDefinitionId": "/providers/Microsoft.Authorization/policySetDefinitions/9cb3cc7a-b39b-4b82-bc89-e5a5d9ff7b97", "definitionVersion": "3.*.*", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Azure Defender {enforcementMode} be enabled on SQL Servers and SQL Managed Instances."}], "parameters": {}, "scope": "${current_scope_resource_id}", "notScopes": []}}