{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "Deploy-vmHybr-Monitoring", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Enable Azure Monitor for Hybrid Virtual Machines in the specified scope (Management group, Subscription or resource group).", "displayName": "Enable Azure Monitor for Hybrid Virtual Machines", "policyDefinitionId": "/providers/Microsoft.Authorization/policySetDefinitions/2b00397d-c309-49c4-aa5a-f0b2c5bc6321", "definitionVersion": "1.*.*", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Azure Monitor {enforcementMode} be enabled for Hybrid Virtual Machines."}], "parameters": {"dcrResourceId": {"value": "${azure_monitor_data_collection_rule_vm_insights_resource_id}"}, "enableProcessesAndDependencies": {"value": true}}, "scope": "${current_scope_resource_id}", "notScopes": []}}