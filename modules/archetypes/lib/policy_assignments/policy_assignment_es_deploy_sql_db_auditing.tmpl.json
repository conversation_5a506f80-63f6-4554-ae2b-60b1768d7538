{"name": "Deploy-SQL-DB-Auditing", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Auditing on your SQL Server should be enabled to track database activities across all databases on the server and save them in an audit log.", "displayName": "Auditing on SQL server should be enabled", "notScopes": [], "parameters": {}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a6fb4358-5bf4-4ad7-ba82-2cd2f41ce5e9", "nonComplianceMessages": [{"message": "SQL Server Auditing {enforcementMode} be enabled."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "SystemAssigned"}}