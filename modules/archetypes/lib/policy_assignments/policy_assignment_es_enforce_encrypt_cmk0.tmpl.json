{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-Encrypt-CMK0", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables additional ALZ guardrails for Customer Managed Keys.", "displayName": "Enforce recommended guardrails for Customer Managed Keys", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Encryption-CMK_20250218", "enforcementMode": "DoNotEnforce", "nonComplianceMessages": [{"message": "Recommended guardrails {enforcementMode} be enforced for Customer Managed Keys."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}