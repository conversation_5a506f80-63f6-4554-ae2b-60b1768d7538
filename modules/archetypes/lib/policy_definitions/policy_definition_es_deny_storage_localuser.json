{"name": "Deny-Storage-LocalUser", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Local users should be restricted for Storage Accounts", "description": "Azure Storage accounts should disable local users for features like SFTP. Enforce this for increased data exfiltration protection.", "metadata": {"version": "1.0.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"field": "Microsoft.Storage/storageAccounts/isLocalUserEnabled", "exists": "false"}, {"field": "Microsoft.Storage/storageAccounts/isLocalUserEnabled", "notEquals": false}]}]}, "then": {"effect": "[parameters('effect')]"}}}}