{"name": "Deny-MachineLearning-Aks", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deny AKS cluster creation in Azure Machine Learning", "description": "Deny AKS cluster creation in Azure Machine Learning and enforce connecting to existing clusters.", "metadata": {"version": "1.0.0", "category": "Machine Learning", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.MachineLearningServices/workspaces/computes"}, {"field": "Microsoft.MachineLearningServices/workspaces/computes/computeType", "equals": "AKS"}, {"anyOf": [{"field": "Microsoft.MachineLearningServices/workspaces/computes/resourceId", "exists": false}, {"value": "[empty(field('Microsoft.MachineLearningServices/workspaces/computes/resourceId'))]", "equals": true}]}]}, "then": {"effect": "[parameters('effect')]"}}}}