{"name": "Append-KV-SoftDelete", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "KeyVault SoftDelete should be enabled", "description": "This policy enables you to ensure when a Key Vault is created with out soft delete enabled it will be added.", "metadata": {"version": "1.0.0", "category": "<PERSON>", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"field": "Microsoft.KeyVault/vaults/enableSoftDelete", "notEquals": true}]}]}, "then": {"effect": "append", "details": [{"field": "Microsoft.KeyVault/vaults/enableSoftDelete", "value": true}]}}}}