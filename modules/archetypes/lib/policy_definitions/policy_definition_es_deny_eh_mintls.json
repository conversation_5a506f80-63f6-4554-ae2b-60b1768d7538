{"name": "Deny-EH-minTLS", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Event Hub namespaces should use a valid TLS version", "description": "Event Hub namespaces should use a valid TLS version.", "metadata": {"version": "1.1.0", "category": "Event Hub", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "minTlsVersion": {"type": "string", "metadata": {"displayName": "Minimum TLS Version", "description": "Minimum TLS version to be used by Event Hub"}, "defaultValue": "1.2"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.EventHub/namespaces"}, {"anyOf": [{"field": "Microsoft.EventHub/namespaces/minimumTlsVersion", "less": "[parameters('minTlsVersion')]"}, {"field": "Microsoft.EventHub/namespaces/minimumTlsVersion", "exists": "false"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}