{"name": "Deploy-VNET-HubSpoke", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deploy Virtual Network with peering to the hub", "description": "This policy deploys virtual network and peer to the hub", "metadata": {"version": "1.1.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"vNetName": {"type": "String", "metadata": {"displayName": "vNetName", "description": "Name of the landing zone vNet"}}, "vNetRgName": {"type": "String", "metadata": {"displayName": "vNetRgName", "description": "Name of the landing zone vNet RG"}}, "vNetLocation": {"type": "String", "metadata": {"displayName": "vNetLocation", "description": "Location for the vNet"}}, "vNetCidrRange": {"type": "String", "metadata": {"displayName": "vNetCidrRange", "description": "CIDR Range for the vNet"}}, "hubResourceId": {"type": "String", "metadata": {"displayName": "hubResourceId", "description": "Resource ID for the HUB vNet"}}, "dnsServers": {"type": "Array", "metadata": {"displayName": "DNSServers", "description": "Default domain servers for the vNET."}, "defaultValue": []}, "vNetPeerUseRemoteGateway": {"type": "Boolean", "metadata": {"displayName": "vNetPeerUseRemoteGateway", "description": "Enable gateway transit for the LZ network"}, "defaultValue": false}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}]}, "then": {"effect": "deployIfNotExists", "details": {"type": "Microsoft.Network/virtualNetworks", "name": "[parameters('vNetName')]", "deploymentScope": "subscription", "existenceScope": "resourceGroup", "ResourceGroupName": "[parameters('vNetRgName')]", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c"], "existenceCondition": {"allOf": [{"field": "name", "like": "[parameters('vNetName')]"}, {"field": "location", "equals": "[parameters('vNetLocation')]"}]}, "deployment": {"location": "northeurope", "properties": {"mode": "Incremental", "parameters": {"vNetRgName": {"value": "[parameters('vNetRgName')]"}, "vNetName": {"value": "[parameters('vNetName')]"}, "vNetLocation": {"value": "[parameters('vNetLocation')]"}, "vNetCidrRange": {"value": "[parameters('vNetCidrRange')]"}, "hubResourceId": {"value": "[parameters('hubResourceId')]"}, "dnsServers": {"value": "[parameters('dnsServers')]"}, "vNetPeerUseRemoteGateway": {"value": "[parameters('vNetPeerUseRemoteGateway')]"}}, "template": {"$schema": "http://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json", "contentVersion": "*******", "parameters": {"vNetRgName": {"type": "String"}, "vNetName": {"type": "String"}, "vNetLocation": {"type": "String"}, "vNetCidrRange": {"type": "String"}, "vNetPeerUseRemoteGateway": {"type": "bool", "defaultValue": false}, "hubResourceId": {"type": "String"}, "dnsServers": {"type": "Array", "defaultValue": []}}, "variables": {}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[concat('alz-vnet-rg-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]", "location": "[parameters('vNetLocation')]", "dependsOn": [], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2021-04-01", "name": "[parameters('vNetRgName')]", "location": "[parameters('vNetLocation')]", "properties": {}}], "outputs": {}}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[concat('alz-vnet-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]", "dependsOn": ["[concat('alz-vnet-rg-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.Network/virtualNetworks", "apiVersion": "2021-02-01", "name": "[parameters('vNetName')]", "location": "[parameters('vNetLocation')]", "dependsOn": [], "properties": {"addressSpace": {"addressPrefixes": ["[parameters('vNetCidrRange')]"]}, "dhcpOptions": {"dnsServers": "[parameters('dnsServers')]"}}}, {"type": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings", "apiVersion": "2021-02-01", "name": "[concat(parameters('vNetName'), '/peerToHub')]", "dependsOn": ["[parameters('vNetName')]"], "properties": {"remoteVirtualNetwork": {"id": "[parameters('hubResourceId')]"}, "allowVirtualNetworkAccess": true, "allowForwardedTraffic": true, "allowGatewayTransit": false, "useRemoteGateways": "[parameters('vNetPeerUseRemoteGateway')]"}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[concat('alz-hub-peering-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]", "subscriptionId": "[split(parameters('hubResourceId'),'/')[2]]", "resourceGroup": "[split(parameters('hubResourceId'),'/')[4]]", "dependsOn": ["[parameters('vNetName')]"], "properties": {"mode": "Incremental", "expressionEvaluationOptions": {"scope": "inner"}, "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"remoteVirtualNetwork": {"type": "String", "defaultValue": false}, "hubName": {"type": "String", "defaultValue": false}}, "variables": {}, "resources": [{"type": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings", "name": "[[concat(parameters('hubName'),'/',last(split(parameters('remoteVirtualNetwork'),'/')))]", "apiVersion": "2021-02-01", "properties": {"allowVirtualNetworkAccess": true, "allowForwardedTraffic": true, "allowGatewayTransit": true, "useRemoteGateways": false, "remoteVirtualNetwork": {"id": "[[parameters('remoteVirtualNetwork')]"}}}], "outputs": {}}, "parameters": {"remoteVirtualNetwork": {"value": "[concat(subscription().id,'/resourceGroups/',parameters('vNetRgName'), '/providers/','Microsoft.Network/virtualNetworks/', parameters('vNetName'))]"}, "hubName": {"value": "[split(parameters('hubResourceId'),'/')[8]]"}}}}], "outputs": {}}}, "resourceGroup": "[parameters('vNetRgName')]"}], "outputs": {}}}}}}}}}