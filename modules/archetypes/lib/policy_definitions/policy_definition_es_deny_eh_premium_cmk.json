{"name": "Deny-EH-Premium-CMK", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Event Hub namespaces (Premium) should use a customer-managed key for encryption", "description": "Event Hub namespaces (Premium) should use a customer-managed key for encryption.", "metadata": {"version": "1.0.0", "category": "Event Hub", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.EventHub/namespaces"}, {"field": "Microsoft.EventHub/namespaces/sku.name", "equals": "Premium"}, {"not": {"field": "Microsoft.EventHub/namespaces/encryption.keySource", "equals": "Microsoft.Keyvault"}}]}, "then": {"effect": "[parameters('effect')]"}}}}