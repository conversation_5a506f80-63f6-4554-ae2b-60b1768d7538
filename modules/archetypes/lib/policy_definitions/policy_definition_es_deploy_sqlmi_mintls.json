{"name": "Deploy-SqlMi-minTLS", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "SQL managed instances deploy a specific min TLS version requirement.", "description": "Deploy a specific min TLS version requirement and enforce SSL on SQL managed instances. Enables secure server to client by enforce  minimal Tls Version to secure the connection between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your database server.", "metadata": {"version": "1.3.0", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect SQL servers", "description": "Enable or disable the execution of the policy minimum TLS version SQL servers"}}, "minimalTlsVersion": {"type": "String", "defaultValue": "1.2", "allowedValues": ["1.2", "1.1", "1.0"], "metadata": {"displayName": "Select version for SQL server", "description": "Select version minimum TLS version SQL servers to enforce"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Sql/managedInstances"}, {"field": "Microsoft.Sql/managedInstances/minimalTlsVersion", "less": "[parameters('minimalTlsVersion')]"}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Sql/managedInstances", "evaluationDelay": "AfterProvisioningSuccess", "existenceCondition": {"allOf": [{"field": "Microsoft.Sql/managedInstances/minimalTlsVersion", "equals": "[parameters('minimalTlsVersion')]"}]}, "name": "current", "roleDefinitionIds": ["/providers/microsoft.authorization/roleDefinitions/4939a1f6-9ae0-4e48-a1e0-f2cbe897382d"], "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"resourceName": {"type": "String"}, "minimalTlsVersion": {"type": "String"}, "location": {"type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.Sql/managedInstances", "apiVersion": "2020-02-02-preview", "name": "[concat(parameters('resourceName'))]", "location": "[parameters('location')]", "properties": {"minimalTlsVersion": "[parameters('minimalTlsVersion')]"}}], "outputs": {}}, "parameters": {"resourceName": {"value": "[field('name')]"}, "minimalTlsVersion": {"value": "[parameters('minimalTlsVersion')]"}, "location": {"value": "[field('location')]"}}}}}}}}}