{"name": "Deploy-DDoSProtection", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deploy an Azure DDoS Network Protection", "description": "Deploys an Azure DDoS Network Protection", "metadata": {"version": "1.0.1", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"ddosName": {"type": "String", "metadata": {"displayName": "ddosName", "description": "DDoSVnet"}}, "ddosRegion": {"type": "String", "metadata": {"displayName": "ddosRegion", "description": "DDoSVnet location", "strongType": "location"}}, "rgName": {"type": "String", "metadata": {"displayName": "rgName", "description": "Provide name for resource group."}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Network/ddosProtectionPlans", "deploymentScope": "subscription", "existenceScope": "resourceGroup", "resourceGroupName": "[parameters('rgName')]", "name": "[parameters('ddosName')]", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/4d97b98b-1d4f-4787-a291-c67834d212e7"], "deployment": {"location": "northeurope", "properties": {"mode": "Incremental", "parameters": {"rgName": {"value": "[parameters('rgName')]"}, "ddosname": {"value": "[parameters('ddosname')]"}, "ddosregion": {"value": "[parameters('ddosRegion')]"}}, "template": {"$schema": "http://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json", "contentVersion": "*******", "parameters": {"rgName": {"type": "String"}, "ddosname": {"type": "String"}, "ddosRegion": {"type": "String"}}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2018-05-01", "name": "[parameters('rgName')]", "location": "[deployment().location]", "properties": {}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2018-05-01", "name": "ddosprotection", "resourceGroup": "[parameters('rgName')]", "dependsOn": ["[resourceId('Microsoft.Resources/resourceGroups/', parameters('rgName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json", "contentVersion": "*******", "parameters": {}, "resources": [{"type": "Microsoft.Network/ddosProtectionPlans", "apiVersion": "2019-12-01", "name": "[parameters('ddosName')]", "location": "[parameters('ddosRegion')]", "properties": {}}], "outputs": {}}}}], "outputs": {}}}}}}}}}