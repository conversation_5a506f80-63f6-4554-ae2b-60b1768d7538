{"name": "Deny-PublicEndpoint-MariaDB", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "[Deprecated] Public network access should be disabled for MariaDB", "description": "This policy denies the creation of Maria DB accounts with exposed public endpoints. Superseded by https://www.azadvertizer.net/azpolicyadvertizer/fdccbe47-f3e3-4213-ad5d-ea459b2fa077.html", "metadata": {"version": "1.0.0-deprecated", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "deprecated": true, "supersededBy": "fdccbe47-f3e3-4213-ad5d-ea459b2fa077", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.DBforMariaDB/servers"}, {"field": "Microsoft.DBforMariaDB/servers/publicNetworkAccess", "notequals": "Disabled"}]}, "then": {"effect": "[parameters('effect')]"}}}}