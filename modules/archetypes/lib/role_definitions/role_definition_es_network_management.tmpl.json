{"name": "dc726155-3983-5405-b446-9bb27b94e02c", "type": "Microsoft.Authorization/roleDefinitions", "apiVersion": "2018-01-01-preview", "properties": {"roleName": "Network-Management", "description": "Platform-wide global connectivity management: virtual networks, UDRs, NSGs, NVAs, VPN, Azure ExpressRoute, and others", "type": "customRole", "permissions": [{"actions": ["*/read", "Microsoft.Network/*", "Microsoft.Resources/deployments/*", "Microsoft.Support/*"], "notActions": [], "dataActions": [], "notDataActions": []}], "assignableScopes": ["${current_scope_resource_id}"]}}