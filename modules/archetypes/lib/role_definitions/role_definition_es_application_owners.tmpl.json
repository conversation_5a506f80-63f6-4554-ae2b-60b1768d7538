{"name": "c9a07a05-a1fc-53fe-a565-5eed25597c03", "type": "Microsoft.Authorization/roleDefinitions", "apiVersion": "2018-01-01-preview", "properties": {"roleName": "Application-Owners", "description": "Contributor role granted for application/operations team at resource group level", "type": "customRole", "permissions": [{"actions": ["*"], "notActions": ["Microsoft.Authorization/*/write", "Microsoft.Network/publicIPAddresses/write", "Microsoft.Network/virtualNetworks/write", "Microsoft.KeyVault/locations/deletedVaults/purge/action"], "dataActions": [], "notDataActions": []}], "assignableScopes": ["${current_scope_resource_id}"]}}