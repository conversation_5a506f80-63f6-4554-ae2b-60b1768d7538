<!-- markdownlint-disable first-line-h1 -->
This project welcomes contributions and suggestions. Most contributions require you to agree to a Contributor License Agreement (CLA) declaring that you have the right to, and actually do, grant us the rights to use your contribution. For details, visit [https://cla.opensource.microsoft.com](https://cla.opensource.microsoft.com).

When you submit a pull request, a CLA bot will automatically determine whether you need to provide a CLA and decorate the PR appropriately (e.g., status check, comment). Simply follow the instructions provided by the bot. You will only need to do this once across all repos using our CLA.

This project has adopted the Microsoft Open Source Code of Conduct. For more information see the Code of Conduct FAQ <NAME_EMAIL> with any additional questions or comments.

Please familiarize yourself with our [Code of Conduct][Code-of-Conduct] and the [MIT License][License] associated with this repository to ensure all code contributions are submitted in accordance with these terms.

## Next steps

- [Raising an Issue](Raising-an-Issue)
- [Feature Requests](Feature-Requests)
- [Contributing to Code](Contributing-to-Code)
- [Contributing to Documentation](Contributing-to-Documentation)

<!--Reference links in article-->

[Code-of-Conduct]: https://github.com/Azure/terraform-azurerm-caf-enterprise-scale/blob/main/CODE_OF_CONDUCT.md "Our Code-of-Conduct"
[License]:         https://github.com/Azure/terraform-azurerm-caf-enterprise-scale/blob/main/LICENSE "Our license"
