# <PERSON><PERSON><PERSON> hình cơ bản
root_id                      = "sgr"
root_name                    = "SunGroup"
primary_location             = "southeastasia"
secondary_location           = "eastasia"

# Subscription IDs - Thay thế bằng subscription IDs thực tế của bạn
subscription_id_connectivity = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_identity     = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
subscription_id_management   = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

# Security contact
email_security_contact       = "<EMAIL>"

# Log retention
log_retention_in_days        = 60

# DDoS Protection
enable_ddos_protection       = true

# Tags
connectivity_resources_tags = {
  deployedBy = "CTS"
  type       = "connectivity-resources"
  environment = "production"
}

management_resources_tags = {
  deployedBy = "CTS"
  type       = "management-resources"
  environment = "production"
}

# Azure AD Groups Configuration
# Set to empty map to disable Azure AD group creation
azure_ad_groups = {}

# Role assignment scope
# Set to true to assign Azure roles to all subscriptions in the tenant
# Set to false to assign only to the current subscription
assign_roles_to_all_subscriptions = false

# Directory role assignments
# Set to true to enable Azure AD directory role assignments
# Requires Privileged Role Administrator permissions
enable_directory_role_assignments = false
