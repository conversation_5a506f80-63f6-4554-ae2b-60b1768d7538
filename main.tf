# Configure Terraform to set the required AzureRM provider
# version and features{} block

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 2.47"
    }
  }
}

# Define the provider configuration

provider "azurerm" {
  features {}
}

# Get the current client configuration from the AzureRM provider

data "azurerm_client_config" "current" {}

# Logic to handle 1-3 platform subscriptions as available

locals {
  subscription_id_connectivity = coalesce(var.subscription_id_connectivity, local.subscription_id_management)
  subscription_id_identity     = coalesce(var.subscription_id_identity, local.subscription_id_management)
  subscription_id_management   = coalesce(var.subscription_id_management, data.azurerm_client_config.current.subscription_id)
}

# The following module declarations act to orchestrate the
# independently defined module instances for core,
# connectivity and management resources

# Comment out connectivity module for testing
# module "connectivity" {
#   source = "./connectivity"
#
#   connectivity_resources_tags  = var.connectivity_resources_tags
#   enable_ddos_protection       = var.enable_ddos_protection
#   primary_location             = var.primary_location
#   root_id                      = var.root_id
#   secondary_location           = var.secondary_location
#   subscription_id_connectivity = local.subscription_id_connectivity
# }

# Comment out management module for testing
# module "management" {
#   source = "./management"
#
#   email_security_contact         = var.email_security_contact
#   security_alerts_email_address  = var.email_security_contact
#   log_retention_in_days          = var.log_retention_in_days
#   management_resources_tags      = var.management_resources_tags
#   management_resources_location  = var.primary_location
#   primary_location               = var.primary_location
#   root_id                        = var.root_id
#   subscription_id_management     = local.subscription_id_management
# }

# Keep only core module for testing Enterprise Scale deployment
module "core" {
  source = "./core"

  root_id   = var.root_id
  root_name = var.root_name
  # Temporarily disable Azure AD groups variables
  # azure_ad_groups                      = var.azure_ad_groups
  # assign_roles_to_all_subscriptions    = var.assign_roles_to_all_subscriptions
  # enable_directory_role_assignments    = var.enable_directory_role_assignments
}
