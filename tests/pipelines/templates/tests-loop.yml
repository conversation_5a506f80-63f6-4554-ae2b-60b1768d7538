---
parameters:
  - name: module_path
    type: string
  - name: run_type
    type: string

steps:
  - task: AzureCLI@2
    displayName: "[terraform init]"
    inputs:
      scriptType: bash
      scriptLocation: scriptPath
      scriptPath: "tests/scripts/tf-init.sh"
      failOnStandardError: false
      addSpnToEnvironment: true
      azureSubscription: ado-mscet-cae-estf
    env:
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), in('${{ parameters.run_type }}', 'unit', 'e2e', 'destroy'))

  - task: AzureCLI@2
    displayName: "[terraform plan]"
    inputs:
      scriptType: bash
      scriptLocation: scriptPath
      scriptPath: "tests/scripts/tf-plan.sh"
      failOnStandardError: false
      addSpnToEnvironment: true
      azureSubscription: ado-mscet-cae-estf
    env:
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), in('${{ parameters.run_type }}', 'unit'))

  - task: AzureCLI@2
    displayName: "[terraform apply]"
    inputs:
      scriptType: bash
      scriptLocation: scriptPath
      failOnStandardError: false
      addSpnToEnvironment: true
      scriptPath: "tests/scripts/tf-apply.sh"
      azureSubscription: ado-mscet-cae-estf
    env:
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), eq('${{ parameters.run_type }}', 'e2e'))

  - task: AzureCLI@2
    displayName: "[terraform destroy]"
    inputs:
      scriptType: bash
      scriptLocation: scriptPath
      failOnStandardError: false
      addSpnToEnvironment: true
      scriptPath: "tests/scripts/tf-destroy.sh"
      azureSubscription: ado-mscet-cae-estf
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), eq('${{ parameters.run_type }}', 'destroy'))
