---
steps:
  - task: AzurePowerShell@5
    name: build_strategy
    displayName: "Generate Build Strategy"
    inputs:
      azureSubscription: ado-mscet-cae-estf
      scriptType: FilePath
      scriptPath: "tests/scripts/azp-strategy.ps1"
      failOnStandardError: false
      azurePowerShellVersion: "LatestVersion" # Adding version specification for clarity
    env:
      BILLING_SCOPE: $(BILLING_SCOPE)
