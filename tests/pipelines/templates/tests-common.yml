---
parameters:
  - name: run_type
    type: string
    default: default

steps:
  - task: Bash@3
    displayName: "Install Terraform"
    inputs:
      targetType: "inline"
      script: "make tf-install"

  - task: AzureCLI@2
    displayName: "Prepare Terraform Environment"
    inputs:
      azureSubscription: ado-mscet-cae-estf
      scriptLocation: scriptPath
      scriptPath: "tests/scripts/tf-prepare.sh"
      scriptType: bash
      failOnStandardError: false
