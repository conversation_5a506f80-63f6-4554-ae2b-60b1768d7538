{"name": "Deploy-SQL-Auditing", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Deploy Auditing on SQL servers.", "displayName": "Deploy Auditing on SQL servers", "notScopes": [], "parameters": {"retentionDays": {"value": null}, "storageAccountsResourceGroup": {"value": null}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f4c68484-132f-41f9-9b6d-3e4b1cb55036", "comment:nonComplianceMessages": "Please note that this test assignment does not have a nonComplianceMessages property by design. This is for testing of the default non-compliance message behaviour.", "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "SystemAssigned"}}