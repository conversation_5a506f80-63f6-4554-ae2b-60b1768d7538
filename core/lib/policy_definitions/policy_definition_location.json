{"name": "location", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Allows location for EWH resources", "description": "Resources must be in the allowed locations", "policyType": "Custom", "mode": "All", "metadata": {"category": "Tags", "version": "1.0.0"}, "parameters": {"allowedLocations": {"type": "Array", "metadata": {"displayName": "Allowed Locations", "description": "List of allowed Azure locations for resources and resource groups"}}}, "policyRule": {"if": {"allOf": [{"field": "location", "exists": "true"}, {"field": "location", "notIn": "[parameters('allowedLocations')]"}]}, "then": {"effect": "<PERSON><PERSON>"}}}}