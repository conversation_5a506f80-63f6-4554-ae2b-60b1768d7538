{"name": "Block-Public-IP-VMs", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "Blocks the assignment of public IP addresses to virtual machines to enhance security.", "displayName": "Block Public IP Assignment to VMs (Custom)", "notScopes": [], "parameters": {}, "policyDefinitionId": "${current_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/block_public_ip_assignment_to_vms", "nonComplianceMessages": [{"message": "Virtual machines {enforcementMode} not have public IP addresses assigned."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}