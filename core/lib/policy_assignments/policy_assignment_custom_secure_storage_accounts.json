{"name": "Secure-Storage-Accounts", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy ensures that secure transfer (HTTPS only) is enabled on storage accounts to protect data in transit.", "displayName": "Require secure transfer on Storage Accounts", "notScopes": [], "parameters": {"effect": {"value": "<PERSON><PERSON>"}}, "policyDefinitionId": "${current_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/secure_storage_accounts", "nonComplianceMessages": [{"message": "Storage accounts {enforcementMode} have secure transfer enabled."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}