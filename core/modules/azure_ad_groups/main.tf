# Azure AD Groups Module
# This module creates and manages Azure AD groups with role assignments

terraform {
  required_providers {
    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 2.47"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# Get current client configuration
data "azuread_client_config" "current" {}

# Get current Azure client config
data "azurerm_client_config" "current" {}

# Get all subscriptions if needed
data "azurerm_subscriptions" "available" {
  count = var.assign_roles_to_all_subscriptions ? 1 : 0
}

# Get Azure AD directory roles if needed
data "azuread_directory_roles" "all" {
  count = var.enable_directory_role_assignments ? 1 : 0
}

# Create Azure AD Groups
resource "azuread_group" "groups" {
  for_each = var.groups

  display_name         = each.value.display_name
  description          = each.value.description
  security_enabled     = each.value.security_enabled
  assignable_to_role   = each.value.assignable_to_role
  mail_enabled         = lookup(each.value, "mail_enabled", false)
  
  # Add owners to the group
  owners = concat(
    [data.azuread_client_config.current.object_id],
    lookup(each.value, "additional_owners", [])
  )
  
  # Add members to the group
  members = lookup(each.value, "members", [])


}

# Azure RBAC Role Assignments - Current Subscription
resource "azurerm_role_assignment" "subscription_roles" {
  for_each = local.subscription_role_assignments

  scope                = each.value.scope
  role_definition_name = each.value.role_name
  principal_id         = azuread_group.groups[each.value.group_key].object_id
  
  depends_on = [azuread_group.groups]
}

# Azure RBAC Role Assignments - All Subscriptions
resource "azurerm_role_assignment" "all_subscriptions_roles" {
  for_each = local.all_subscriptions_role_assignments

  scope                = each.value.scope
  role_definition_name = each.value.role_name
  principal_id         = azuread_group.groups[each.value.group_key].object_id
  
  depends_on = [azuread_group.groups]
}

# Azure AD Directory Role Assignments
resource "azuread_directory_role_assignment" "directory_roles" {
  for_each = local.directory_role_assignments

  role_id             = each.value.role_id
  principal_object_id = azuread_group.groups[each.value.group_key].object_id
  
  depends_on = [azuread_group.groups]
}

# Local values for role assignments
locals {
  # Current subscription role assignments
  subscription_role_assignments = var.assign_roles_to_all_subscriptions ? {} : {
    for assignment in flatten([
      for group_key, group in var.groups : [
        for role in lookup(group, "azure_roles", []) : {
          key       = "${group_key}.${role}.current"
          group_key = group_key
          role_name = role
          scope     = "/subscriptions/${data.azurerm_client_config.current.subscription_id}"
        }
      ]
    ]) : assignment.key => assignment
  }

  # All subscriptions role assignments
  all_subscriptions_role_assignments = var.assign_roles_to_all_subscriptions ? {
    for assignment in flatten([
      for group_key, group in var.groups : [
        for role in lookup(group, "azure_roles", []) : [
          for sub in data.azurerm_subscriptions.available[0].subscriptions : {
            key       = "${group_key}.${role}.${sub.subscription_id}"
            group_key = group_key
            role_name = role
            scope     = "/subscriptions/${sub.subscription_id}"
          }
        ]
      ]
    ]) : assignment.key => assignment
  } : {}

  # Directory role assignments
  directory_role_assignments = var.enable_directory_role_assignments ? {
    for assignment in flatten([
      for group_key, group in var.groups : [
        for role_name in lookup(group, "directory_roles", []) : {
          key       = "${group_key}.${role_name}"
          group_key = group_key
          role_id   = local.directory_role_map[role_name]
        }
      ]
    ]) : assignment.key => assignment
  } : {}

  # Map directory role names to IDs
  directory_role_map = var.enable_directory_role_assignments ? {
    for role in data.azuread_directory_roles.all[0].roles :
    role.display_name => role.object_id
  } : {}
}
